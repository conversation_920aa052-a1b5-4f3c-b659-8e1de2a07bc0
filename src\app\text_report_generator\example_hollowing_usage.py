"""
新业务逻辑使用示例：挖空业务

展示如何使用新的【挖空】业务逻辑
"""

import asyncio
import logging
from typing import List

from .models import (
    DocumentProcessingRequest,
    IndicatorInfo,
    ChapterInfo
)
from .services import TextReportGenerator

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def example_hollowing_workflow():
    """
    示例：完整的挖空业务流程
    """
    print("🚀 开始挖空业务流程示例...")
    
    # 1. 准备测试数据
    indicators = [
        IndicatorInfo(
            id="8849-123456-123456",
            id_name="个人贷款业务总余额",
            id_desc="个人贷款业务的总余额",
            id_value="15000万元"
        ),
        IndicatorInfo(
            id="8849-123456-123457",
            id_name="企业贷款业务总余额", 
            id_desc="企业贷款业务的总余额",
            id_value="25000万元"
        ),
        IndicatorInfo(
            id="8849-123456-123458",
            id_name="贷款业务增长率",
            id_desc="贷款业务的同比增长率",
            id_value="12.5%"
        ),
        IndicatorInfo(
            id="8849-123456-123459",
            id_name="不良贷款率",
            id_desc="不良贷款占总贷款的比率",
            id_value="1.2%"
        )
    ]
    
    markdown_content = """
## 贷款业务总体情况

本月我行贷款业务发展态势良好，各项指标均达到预期目标。个人贷款业务总余额实现稳步增长，企业贷款业务也保持了良好的发展势头。

## 个人贷款业务

个人贷款业务是我行的重点业务之一。本月个人贷款业务总余额达到新的历史高点，相比上月实现了显著增长。其中，个人住房贷款占据主要份额，个人消费贷款也呈现良好增长态势。

## 企业贷款业务

企业贷款业务方面，我行继续加大对实体经济的支持力度。企业贷款业务总余额持续增长，主要投向制造业、服务业等重点领域。

## 风险控制情况

在业务快速发展的同时，我行始终将风险控制放在首位。通过完善风险管理体系，不良贷款率保持在合理水平，资产质量总体稳定。

## 业务展望

展望未来，我行将继续优化贷款结构，提升服务质量。预计贷款业务增长率将保持在合理区间，为经济发展提供有力支撑。
"""
    
    print("📄 准备处理的Markdown内容:")
    print(markdown_content[:200] + "...")
    print(f"📊 可用指标数量: {len(indicators)}")
    
    try:
        # 注意：这里需要实际的客户端实例，示例中使用模拟
        # 在实际使用中，需要从配置中获取真实的客户端
        
        # 模拟客户端（实际使用时需要替换为真实客户端）
        from unittest.mock import Mock, AsyncMock
        
        mock_rdb_client = Mock()
        mock_vdb_client = Mock()
        mock_embedding_client = Mock()
        mock_llm_client = Mock()
        
        # 模拟LLM响应
        mock_summary_response = Mock()
        mock_summary_response.message.content = "这是一个关于贷款业务的章节摘要，描述了业务发展情况和主要指标。"
        
        mock_match_response = Mock()
        mock_match_response.message.content = '["8849-123456-123456", "8849-123456-123458"]'
        
        mock_hollow_response = Mock()
        mock_hollow_response.message.content = """本月我行贷款业务发展态势良好，各项指标均达到预期目标。个人贷款业务总余额实现稳步增长，达到<!--8849-123456-123456-->，相比上月增长率为<!--8849-123456-123458-->。"""
        
        mock_llm_client.ainvoke = AsyncMock(side_effect=[
            mock_summary_response,  # 第一个章节摘要
            mock_summary_response,  # 第二个章节摘要  
            mock_summary_response,  # 第三个章节摘要
            mock_summary_response,  # 第四个章节摘要
            mock_summary_response,  # 第五个章节摘要
            mock_match_response,    # 指标匹配
            mock_hollow_response,   # 挖空处理
            mock_match_response,    # 指标匹配
            mock_hollow_response,   # 挖空处理
            mock_match_response,    # 指标匹配
            mock_hollow_response,   # 挖空处理
            mock_match_response,    # 指标匹配
            mock_hollow_response,   # 挖空处理
            mock_match_response,    # 指标匹配
            mock_hollow_response,   # 挖空处理
        ])
        
        # 2. 创建TextReportGenerator实例
        generator = TextReportGenerator(
            rdb_client=mock_rdb_client,
            vdb_client=mock_vdb_client,
            embedding_client=mock_embedding_client,
            llm_client=mock_llm_client,
            knowledge_id="example_knowledge_id"
        )
        
        # 3. 创建文档处理请求
        request = DocumentProcessingRequest(
            markdown_content=markdown_content,
            indicators=indicators
        )
        
        # 4. 执行新业务流程：处理文档进行挖空
        print("\n🔄 开始处理文档...")
        chapters = await generator.process_document_for_hollowing(request)
        
        print(f"✅ 文档处理完成，生成了 {len(chapters)} 个章节")
        
        # 5. 展示处理结果
        print("\n📋 处理结果:")
        for i, chapter in enumerate(chapters, 1):
            print(f"\n--- 章节 {i}: {chapter.chapter_name} ---")
            print(f"摘要: {chapter.chapter_summary}")
            print(f"使用的指标: {chapter.chapter_indicators_map}")
            
            if chapter.chapter_content and "<!--" in chapter.chapter_content:
                print("🔍 发现挖空标记:")
                print(f"内容: {chapter.chapter_content}")
                
                # 提取挖空标记
                markers = generator.extract_hollow_markers(chapter.chapter_content)
                print(f"挖空标记: {markers}")
                
                # 6. 演示填充挖空标记
                if markers:
                    print("\n🔧 填充挖空标记:")
                    # 构建指标值映射
                    indicator_values = {}
                    for marker in markers:
                        for indicator in indicators:
                            if indicator.id == marker:
                                indicator_values[marker] = indicator.id_value
                                break
                    
                    # 填充标记
                    filled_content = generator.fill_hollow_markers(
                        chapter.chapter_content, indicator_values
                    )
                    print(f"填充后内容: {filled_content}")
            else:
                print(f"内容: {chapter.chapter_content[:100]}...")
        
        # 7. 演示向量搜索功能
        print("\n🔍 演示向量搜索功能:")
        if chapters:
            first_chapter = chapters[0]
            search_results = await generator.search_similar_chapters(
                title=first_chapter.chapter_name,
                summary=first_chapter.chapter_summary,
                min_score=0.8,
                limit=5
            )
            print(f"找到 {len(search_results)} 个相似章节")
        
        # 8. 演示单独的挖空处理
        print("\n🕳️ 演示单独的挖空处理:")
        test_content = "本季度个人贷款业务总余额达到了历史新高，增长率超过预期目标。"
        hollow_result = await generator.hollow_content(test_content, indicators)
        
        print(f"原始内容: {hollow_result.original_content}")
        print(f"挖空后内容: {hollow_result.hollowed_content}")
        print(f"使用的指标: {hollow_result.indicators_used}")
        
        print("\n🎉 挖空业务流程示例完成！")
        
    except Exception as e:
        logger.error(f"示例执行失败: {e}")
        raise


async def example_data_storage():
    """
    示例：数据存储结构
    """
    print("\n📦 数据存储结构示例:")
    
    # 展示chapter_index字段的JSON格式
    chapter_index_example = {
        "8849-123456-123456": "个人贷款业务总余额",
        "8849-123456-123457": "企业贷款业务总余额",
        "8849-123456-123458": "贷款业务增长率"
    }
    
    print("chapter_index字段存储格式:")
    import json
    print(json.dumps(chapter_index_example, ensure_ascii=False, indent=2))
    
    # 展示doc_chunks_info表中的存储示例
    print("\ndoc_chunks_info表存储示例:")
    chunk_infos = [
        {
            "info_type": "chapter_name",
            "info_value": "贷款业务总体情况"
        },
        {
            "info_type": "chapter_content", 
            "info_value": "本月我行贷款业务发展态势良好，个人贷款业务总余额达到<!--8849-123456-123456-->..."
        },
        {
            "info_type": "chapter_summary",
            "info_value": "描述了贷款业务的总体发展情况和主要指标"
        },
        {
            "info_type": "chapter_index",
            "info_value": json.dumps(chapter_index_example, ensure_ascii=False)
        }
    ]
    
    for info in chunk_infos:
        print(f"- {info['info_type']}: {info['info_value'][:50]}...")


def example_business_comparison():
    """
    示例：新旧业务逻辑对比
    """
    print("\n🔄 新旧业务逻辑对比:")
    
    print("【旧业务逻辑 - 填数】:")
    print("1. 接收参考报告选择方式")
    print("2. 获取参考章节")
    print("3. 匹配指标到章节")
    print("4. 使用LLM填充指标数值")
    print("5. 生成完整报告")
    
    print("\n【新业务逻辑 - 挖空】:")
    print("1. 接收markdown内容和完整指标列表")
    print("2. 解析markdown为章节，生成摘要")
    print("3. 对标题和摘要进行向量搜索")
    print("4. 如果找到匹配chunk(0.8+)，直接推荐")
    print("5. 否则，使用LLM识别需要挖空的位置")
    print("6. 用特殊标记(<!--指标ID-->)进行挖空")
    print("7. 存储挖空后的内容和指标映射")
    
    print("\n🎯 核心变化:")
    print("- 从【填充数值】变为【识别挖空位置】")
    print("- 增加了向量搜索匹配环节")
    print("- 新增了chapter_index字段存储指标映射")
    print("- 支持挖空标记的提取和填充")


async def main():
    """主函数"""
    print("=" * 60)
    print("🏦 HSBC Knowledge - 挖空业务逻辑示例")
    print("=" * 60)
    
    # 运行示例
    await example_hollowing_workflow()
    await example_data_storage()
    example_business_comparison()
    
    print("\n" + "=" * 60)
    print("✨ 示例运行完成！")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
