"""
RANGE类型记录聚合模块

专门处理RANGE类型记录的字段聚合逻辑，将多个普通submission项的数据聚合到RANGE记录中。

聚合规则：
- bdr09: table_list聚合 [a,b,c] + [c,d,e] → [a,b,c,d,e]
- bdr10: table_list聚合（同bdr09）
- bdr11: dict聚合 {a:[1,2,3]} + {a:[3,4,5,6]} → {a:[1,2,3,4,5,6]}
- bdr16: 字符串格式 f"表范围:\n {bdr09聚合结果}"
- sdr05: 等于bdr09
- sdr06: 等于bdr10
- sdr08: 等于bdr11
- sdr09: dict聚合 {a:xxx,b:xx} + {a:xx,c:xx} → {a:xxx,b:xx,c:xx}
- sdr10: 默认值''
- sdr12: list聚合，统计元素频次，保留出现次数>总submission数一半的元素

使用示例：
```python
from modules.dd_submission.dd_b.utils.range_aggregator import RangeAggregator

aggregator = RangeAggregator()
result = await aggregator.aggregate_range_fields(range_record, submission_list)
```
"""

import json
import logging
from typing import Any, Dict, List, Optional, Union
from collections import Counter

logger = logging.getLogger(__name__)


class RangeAggregator:
    """RANGE类型记录聚合器"""
    
    def __init__(self):
        """初始化聚合器"""
        pass
    
    async def aggregate_range_fields(
        self, 
        range_record: Dict[str, Any], 
        submission_list: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        聚合RANGE类型记录的字段
        
        Args:
            range_record: 原始RANGE记录
            submission_list: 所有普通submission项的列表
            
        Returns:
            聚合后的RANGE记录
        """
        try:
            logger.debug(f"开始RANGE字段聚合: range_record_id={range_record.get('record_id')}, submission数量={len(submission_list)}")

            # 运行测试（仅在第一次调用时）
            if not hasattr(self, '_test_run'):
                self.test_string_to_list_conversion()
                self._test_run = True

            # 创建聚合结果副本
            aggregated_record = range_record.copy()
            
            # 1. 聚合bdr09 (table_list)
            bdr09_aggregated = self._aggregate_table_list(submission_list, 'bdr09')
            aggregated_record['bdr09'] = self._safe_json_dumps(bdr09_aggregated)
            
            # 2. 聚合bdr10 (table_list)
            bdr10_aggregated = self._aggregate_table_list(submission_list, 'bdr10')
            aggregated_record['bdr10'] = self._safe_json_dumps(bdr10_aggregated)
            
            # 3. 聚合bdr11 (dict)
            bdr11_aggregated = self._aggregate_dict_by_key(submission_list, 'bdr11')
            aggregated_record['bdr11'] = self._safe_json_dumps(bdr11_aggregated)
            
            # 4. 生成bdr16 (表范围字符串)
            bdr16_text = f"表范围:\n{bdr09_aggregated}"
            aggregated_record['bdr16'] = bdr16_text
            
            # 5. sdr05 = bdr09
            aggregated_record['sdr05'] = aggregated_record['bdr09']
            
            # 6. sdr06 = bdr10
            aggregated_record['sdr06'] = aggregated_record['bdr10']
            
            # 7. sdr08 = bdr11
            aggregated_record['sdr08'] = aggregated_record['bdr11']
            
            # 8. 聚合sdr09 (dict)
            sdr09_aggregated = self._aggregate_dict_merge(submission_list, 'sdr09')
            aggregated_record['sdr09'] = self._safe_json_dumps(sdr09_aggregated)
            
            # 9. sdr10 默认值
            aggregated_record['sdr10'] = ''
            
            # 10. 聚合sdr12 (list，频次过滤) - 特殊处理：空列表时返回空字符串
            logger.debug(f"🔍 开始sdr12聚合 - range_record_id={range_record.get('record_id')}")
            logger.debug(f"  submission_list数量: {len(submission_list)}")
            sdr12_aggregated = self._aggregate_list_with_frequency_filter(submission_list, 'sdr12')
            logger.debug(f"  sdr12聚合结果: {sdr12_aggregated}")
            # SDR12特殊处理：如果聚合结果为空列表，设置为空字符串而不是"[]"
            if not sdr12_aggregated:
                aggregated_record['sdr12'] = ''
            else:
                aggregated_record['sdr12'] = self._safe_json_dumps(sdr12_aggregated)
            logger.debug(f"  sdr12最终值: {aggregated_record['sdr12']}")

            logger.debug(f"RANGE字段聚合完成: record_id={range_record.get('record_id')}")
            logger.debug(f"聚合结果: bdr09={len(bdr09_aggregated)}项, bdr10={len(bdr10_aggregated)}项, bdr11={len(bdr11_aggregated)}项, sdr09={len(sdr09_aggregated)}项, sdr12={len(sdr12_aggregated)}项")
            
            return aggregated_record
            
        except Exception as e:
            logger.error(f"RANGE字段聚合失败: {e}")
            return range_record
    
    def _aggregate_table_list(self, submission_list: List[Dict[str, Any]], field_name: str) -> List[str]:
        """
        聚合table_list字段

        Args:
            submission_list: submission列表
            field_name: 字段名（如'bdr09', 'bdr10'）

        Returns:
            聚合后的去重table_list
        """
        aggregated_set = set()

        for submission in submission_list:
            field_value = submission.get(field_name, '')
            table_list = self._safe_parse_list(field_value)

            if table_list:
                aggregated_set.update(table_list)

        result = list(aggregated_set)
        logger.debug(f"{field_name}聚合: {len(submission_list)}个submission → {len(result)}个唯一表")
        return result
    
    def _aggregate_dict_by_key(self, submission_list: List[Dict[str, Any]], field_name: str) -> Dict[str, List]:
        """
        聚合dict字段，按key合并value列表
        
        Args:
            submission_list: submission列表
            field_name: 字段名（如'bdr11'）
            
        Returns:
            聚合后的dict {key: [merged_values]}
        """
        aggregated_dict = {}
        
        for submission in submission_list:
            field_value = submission.get(field_name, '')
            field_dict = self._safe_parse_dict(field_value)
            
            if field_dict:
                for key, value in field_dict.items():
                    if key not in aggregated_dict:
                        aggregated_dict[key] = []
                    
                    # 确保value是列表
                    if isinstance(value, list):
                        aggregated_dict[key].extend(value)
                    else:
                        aggregated_dict[key].append(value)
        
        # 去重每个key的value列表
        for key in aggregated_dict:
            aggregated_dict[key] = list(set(aggregated_dict[key]))
        
        logger.debug(f"{field_name}聚合: {len(submission_list)}个submission → {len(aggregated_dict)}个key")
        return aggregated_dict
    
    def _aggregate_dict_merge(self, submission_list: List[Dict[str, Any]], field_name: str) -> Dict[str, str]:
        """
        聚合dict字段，简单合并（后者覆盖前者）
        
        Args:
            submission_list: submission列表
            field_name: 字段名（如'sdr09'）
            
        Returns:
            聚合后的dict {key: value}
        """
        aggregated_dict = {}
        
        for submission in submission_list:
            field_value = submission.get(field_name, '')
            field_dict = self._safe_parse_dict(field_value)
            
            if field_dict:
                aggregated_dict.update(field_dict)
        
        logger.debug(f"{field_name}聚合: {len(submission_list)}个submission → {len(aggregated_dict)}个key")
        return aggregated_dict
    
    def _aggregate_list_with_frequency_filter(self, submission_list: List[Dict[str, Any]], field_name: str) -> List[str]:
        """
        聚合list字段，按频次过滤（保留出现次数>总数一半的元素）
        专门用于sdr12字段，支持字符串转列表

        Args:
            submission_list: submission列表
            field_name: 字段名（如'sdr12'）

        Returns:
            过滤后的list
        """
        all_elements = []
        valid_submission_count = 0  # 统计有有效数据的submission数量

        logger.debug(f"🔍 开始聚合{field_name}字段:")
        for i, submission in enumerate(submission_list):
            field_value = submission.get(field_name, '')
            logger.debug(f"  submission[{i}].{field_name} = {repr(field_value)} (类型: {type(field_value)})")

            # 现在sdr12在Pipeline中已经是列表格式，使用标准解析
            field_list = self._safe_parse_list(field_value)
            logger.debug(f"  → 解析后: {field_list}")

            if field_list:
                all_elements.extend(field_list)
                valid_submission_count += 1  # 只有有数据的submission才计入
                logger.debug(f"  ✅ 有效数据，计入统计")
            else:
                logger.debug(f"  ❌ 无效数据，不计入统计")

        # 统计频次
        element_counter = Counter(all_elements)
        # 阈值基于有有效数据的submission数量
        threshold = valid_submission_count / 2

        logger.debug(f"  频次统计: {dict(element_counter)}")
        logger.debug(f"  有效submission数量: {valid_submission_count}")
        logger.debug(f"  阈值计算: {valid_submission_count} / 2 = {threshold}")

        # 过滤频次大于阈值的元素
        # 修复：如果没有元素达到阈值，保留所有去重的元素
        filtered_elements = [
            element for element, count in element_counter.items()
            if count > threshold
        ]

        # 如果频次过滤后没有元素，保留所有去重的元素
        if not filtered_elements and all_elements:
            filtered_elements = list(set(all_elements))
            logger.debug(f"  ⚠️ 频次过滤后为空，保留所有去重元素: {filtered_elements}")

        logger.debug(f"✅ {field_name}聚合完成: {len(all_elements)}个元素 → 阈值{threshold} → {len(filtered_elements)}个过滤元素: {filtered_elements}")
        return filtered_elements
    
    def _safe_parse_list(self, value: Union[str, list, None]) -> List:
        """安全解析列表"""
        if not value:
            return []

        if isinstance(value, list):
            return value

        if isinstance(value, str):
            # 先尝试JSON解析
            try:
                parsed = json.loads(value)
                if isinstance(parsed, list):
                    return parsed
                else:
                    # 对于非列表的JSON值，如果是有意义的值
                    if parsed not in [None, False, 0, ""]:
                        # 对于字符串值，返回解析后的字符串；对于其他类型，保持原JSON格式
                        if isinstance(parsed, str):
                            return [parsed]
                        else:
                            return [value.strip()]  # 保持原JSON格式
                    else:
                        return []
            except:
                # JSON解析失败，尝试ast.literal_eval
                try:
                    import ast
                    parsed = ast.literal_eval(value)
                    if isinstance(parsed, list):
                        return parsed
                    else:
                        # 对于非列表的ast解析值，如果是有意义的值
                        if parsed not in [None, False, 0, ""]:
                            if isinstance(parsed, str):
                                return [parsed]
                            else:
                                return [value.strip()]
                        else:
                            return []
                except:
                    # ast解析也失败，将非空字符串作为单个元素
                    stripped = value.strip()
                    if stripped:
                        return [stripped]
                    else:
                        return []

        return []

    def _safe_parse_dict(self, value: Union[str, dict, None]) -> Dict:
        """安全解析字典"""
        if not value:
            return {}
        
        if isinstance(value, dict):
            return value
        
        if isinstance(value, str):
            try:
                parsed = json.loads(value)
                return parsed if isinstance(parsed, dict) else {}
            except:
                return {}
        
        return {}
    
    def _safe_json_dumps(self, value: Any) -> str:
        """安全JSON序列化"""
        try:
            return json.dumps(value, ensure_ascii=False)
        except:
            return str(value)

    def test_string_to_list_conversion(self):
        """测试字符串到列表的转换逻辑"""
        test_cases = [
            # (输入, 期望输出)
            ('adm_lon_accumulative_amt.cust_no = adm_rsk_exposure.cust_no', ['adm_lon_accumulative_amt.cust_no = adm_rsk_exposure.cust_no']),
            ('["item1", "item2"]', ['item1', 'item2']),
            ('[]', []),
            ('', []),
            (None, []),
            (['item1', 'item2'], ['item1', 'item2']),
            ('"single_item"', ['single_item']),
        ]

        logger.debug("🧪 测试字符串到列表转换:")
        for input_val, expected in test_cases:
            result = self._safe_parse_list(input_val)
            status = "✅" if result == expected else "❌"
            logger.debug(f"  {status} 输入: {input_val} → 输出: {result} (期望: {expected})")

        return True
