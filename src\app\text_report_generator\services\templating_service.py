"""
模板化处理服务

实现LLM识别需要填充的位置，并用模板标记包裹的功能
将原来的"挖空"概念改为更专业的"模板化"处理
"""

import logging
import json
import re
from typing import List, Dict, Optional, Any
from ..core.models import IndicatorInfo, TemplatingResult
from base.model_serve.model_runtime.entities import PromptMessage, PromptMessageRole


class TemplatingService:
    """模板化处理服务"""
    
    def __init__(self, llm_client):
        self.llm_client = llm_client
    
    async def create_template(
        self, content: str, indicators: List[IndicatorInfo]
    ) -> TemplatingResult:
        """
        对内容进行模板化处理
        
        Args:
            content: 原始内容
            indicators: 可用的指标列表
            
        Returns:
            模板化处理结果
        """
        try:
            logging.info(f"开始模板化处理，内容长度: {len(content)}, 指标数量: {len(indicators)}")
            
            # 1. 匹配相关指标
            matched_indicators = await self._match_indicators_to_content(content, indicators)
            
            if not matched_indicators:
                logging.info("没有匹配到相关指标，返回原内容")
                return TemplatingResult(
                    original_content=content,
                    templated_content=content,
                    indicators_used={}
                )
            
            # 2. 使用LLM进行模板化处理
            templating_result = await self._llm_create_template(content, matched_indicators, indicators)
            
            logging.info(f"模板化处理完成，使用了 {len(templating_result.indicators_used)} 个指标")
            return templating_result
            
        except Exception as e:
            logging.error(f"模板化处理失败: {e}")
            return TemplatingResult(
                original_content=content,
                templated_content=content,
                indicators_used={}
            )
    
    async def _match_indicators_to_content(
        self, content: str, indicators: List[IndicatorInfo]
    ) -> List[IndicatorInfo]:
        """
        匹配内容相关的指标
        
        Args:
            content: 文本内容
            indicators: 指标列表
            
        Returns:
            匹配的指标列表
        """
        try:
            # 分批处理指标，避免prompt过长
            batch_size = 20
            all_matched_indicators = []
            
            for i in range(0, len(indicators), batch_size):
                batch = indicators[i:i + batch_size]
                matched_ids = await self._match_indicators_batch(content, batch)
                
                # 根据ID找到对应的指标对象
                indicator_map = {ind.id: ind for ind in batch}
                matched_indicators = [indicator_map[id] for id in matched_ids if id in indicator_map]
                all_matched_indicators.extend(matched_indicators)
            
            # 去重
            unique_indicators = {}
            for indicator in all_matched_indicators:
                unique_indicators[indicator.id] = indicator
            
            return list(unique_indicators.values())
            
        except Exception as e:
            logging.error(f"指标匹配失败: {e}")
            return []
    
    async def _match_indicators_batch(
        self, content: str, indicators_batch: List[IndicatorInfo]
    ) -> List[str]:
        """
        批量匹配指标
        
        Args:
            content: 文本内容
            indicators_batch: 指标批次
            
        Returns:
            匹配的指标ID列表
        """
        try:
            indicators_desc = [f"ID:{indicator.id} - {indicator.id_name}" for indicator in indicators_batch]
            
            prompt = f"""
            任务目标：
            请分析文本内容，判断以下哪些指标与该文本内容相关。
            只返回相关的指标ID列表，格式如：["id1","id2","id3"]。
            只有当指标与文本内容相关，且文本中存在该指标对应的数值或描述时，才返回该指标ID。
            
            判断标准：
            1. 指标名称在文本中有直接或间接的提及
            2. 文本中存在可能需要该指标数值的位置
            3. 文本的语义与指标相关
            
            案例1：
            传入文本："本月个人车贷总金额为10000元，较上月上涨10%。"
            传入指标：[ID:8849-123456-001 - 个人车贷总金额, ID:8849-123456-002 - 个人房贷总金额, ID:8849-123456-003 - 个人消费贷总金额]
            返回：["8849-123456-001"]
            
            案例2：
            传入文本："个人房贷业务介绍：我行个人房贷业务发展稳健。"
            传入指标：[ID:8849-123456-001 - 个人车贷总金额, ID:8849-123456-002 - 个人房贷总金额, ID:8849-123456-003 - 个人消费贷总金额]
            返回：["8849-123456-002"]
            
            案例3：
            传入文本："# 业务概述\n本章节介绍业务基本情况。"
            传入指标：[ID:8849-123456-001 - 个人车贷总金额, ID:8849-123456-002 - 个人房贷总金额]
            返回：[]
            
            输入：
            文本内容：
            {content}
            
            可选指标：
            {chr(10).join(indicators_desc)}
            
            相关指标ID：
            """
            
            messages = [PromptMessage(role=PromptMessageRole.USER, content=prompt)]
            result = await self.llm_client.ainvoke(prompt_messages=messages, stream=False)
            response = result.message.content.strip()
            
            # 解析返回的指标ID列表
            try:
                # 尝试解析JSON格式
                matched_ids = json.loads(response)
                if isinstance(matched_ids, list):
                    return matched_ids
            except:
                # 如果JSON解析失败，尝试正则表达式提取
                matches = re.findall(r'"([^"]+)"', response)
                return matches
            
            return []
            
        except Exception as e:
            logging.error(f"批量指标匹配失败: {e}")
            return []
    
    async def _llm_create_template(
        self, content: str, matched_indicators: List[IndicatorInfo], all_indicators: List[IndicatorInfo]
    ) -> TemplatingResult:
        """
        使用LLM对内容进行模板化处理
        
        Args:
            content: 原始内容
            matched_indicators: 匹配的指标列表
            all_indicators: 所有指标列表（用于构建映射）
            
        Returns:
            模板化处理结果
        """
        try:
            if not matched_indicators:
                return TemplatingResult(
                    original_content=content,
                    templated_content=content,
                    indicators_used={}
                )
            
            # 构建指标描述
            indicators_desc = "\n".join([
                f"指标ID: {ind.id}, 指标名称: {ind.id_name}, 指标描述: {ind.id_desc or '无'}"
                for ind in matched_indicators
            ])
            
            prompt = f"""
            任务目标：
            请分析以下文本内容，识别出需要填充指标数值的位置，并用模板标记进行标记。
            
            模板化规则：
            1. 识别文本中可能需要填充数值的位置（如具体的金额、百分比、数量等）
            2. 将这些位置替换为 {{{{指标ID}}}} 的格式
            3. 只对确实需要填充数值的地方进行模板化，不要过度处理
            4. 保持文本的可读性和逻辑性
            5. 优先选择最匹配的指标ID
            
            模板化示例：
            原文："今年的个人贷款业务总余额为15000万元，较去年增长了10%"
            模板化后："今年的个人贷款业务总余额为{{{{8849-123456-123456}}}}，较去年增长了{{{{8849-123456-123457}}}}"
            
            原文："个人车贷业务发展良好，本月新增贷款1000万元"
            模板化后："个人车贷业务发展良好，本月新增贷款{{{{8849-123456-001}}}}"
            
            输入内容：
            {content}
            
            可用指标：
            {indicators_desc}
            
            请返回模板化后的内容（只返回模板化后的文本，不要包含其他说明）：
            """
            
            messages = [PromptMessage(role=PromptMessageRole.USER, content=prompt)]
            result = await self.llm_client.ainvoke(prompt_messages=messages, stream=False)
            templated_content = result.message.content.strip()
            
            # 提取使用的指标
            used_indicator_ids = re.findall(r'\{\{([^}]+)\}\}', templated_content)
            indicators_used = {}
            
            # 构建指标映射
            indicator_map = {ind.id: ind for ind in all_indicators}
            
            for indicator_id in used_indicator_ids:
                if indicator_id in indicator_map:
                    indicators_used[indicator_id] = indicator_map[indicator_id].id_name
            
            return TemplatingResult(
                original_content=content,
                templated_content=templated_content,
                indicators_used=indicators_used
            )
            
        except Exception as e:
            logging.error(f"LLM模板化处理失败: {e}")
            return TemplatingResult(
                original_content=content,
                templated_content=content,
                indicators_used={}
            )
    
    def extract_template_markers(self, content: str) -> List[str]:
        """
        提取内容中的模板标记
        
        Args:
            content: 包含模板标记的内容
            
        Returns:
            模板标记列表
        """
        return re.findall(r'\{\{([^}]+)\}\}', content)
    
    def fill_template_markers(self, content: str, indicator_values: Dict[str, str]) -> str:
        """
        填充模板标记
        
        Args:
            content: 包含模板标记的内容
            indicator_values: 指标值映射 {指标ID: 指标值}
            
        Returns:
            填充后的内容
        """
        try:
            filled_content = content
            
            for indicator_id, value in indicator_values.items():
                marker = f"{{{{{indicator_id}}}}}"
                filled_content = filled_content.replace(marker, str(value))
            
            return filled_content
            
        except Exception as e:
            logging.error(f"填充模板标记失败: {e}")
            return content
