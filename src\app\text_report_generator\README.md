# 文本报告生成器 - 模板化业务逻辑

## 业务背景

原先我们做的是【填数】，将指标的值填进去。现在我们的业务转变为【模板化】，识别需要填充的地方变成了业务中比较重要的部分。

## 核心变化

### 旧业务逻辑（填数）
1. 用户选择参考报告路径
2. 获取参考章节
3. 匹配指标到章节
4. 使用LLM填充指标数值
5. 生成完整报告

### 新业务逻辑（模板化）
1. 接收markdown内容和完整指标列表
2. 解析markdown为章节，生成摘要
3. 对标题和摘要进行向量搜索
4. 如果找到匹配chunk(0.8+)，直接推荐
5. 否则，使用LLM识别需要模板化的位置
6. 用模板标记(`{{指标ID}}`)进行标记
7. 存储模板化后的内容和指标映射

## 项目结构

```
src/app/text_report_generator/
├── core/                          # 核心模块
│   ├── __init__.py
│   └── models.py                  # 数据模型定义
├── services/                      # 服务模块
│   ├── __init__.py
│   ├── document_processing_service.py  # 文档处理服务
│   ├── vector_search_service.py        # 向量搜索服务
│   └── templating_service.py           # 模板化处理服务
├── tests/                         # 测试模块
│   ├── __init__.py
│   └── test_templating_business.py     # 业务测试用例
├── examples/                      # 示例模块
│   ├── __init__.py
│   ├── templating_usage_example.py     # 使用示例
│   └── api_routes_example.py           # API路由示例
├── __init__.py
├── services.py                    # 主服务类
├── chapter_utils.py              # 章节工具
├── example_usage.py              # 原有示例
└── README.md                     # 本文档
```

## 数据存储结构

### doc_chunks表
以章节为单位存储，每个章节得到一个chunk_id。

### doc_chunks_info表
存储章节的具体信息：

- `info_type=chapter_content`: 章节内容（可能包含模板标记）
- `info_type=chapter_name`: 章节标题
- `info_type=chapter_summary`: 章节摘要
- `info_type=chapter_index`: **新增**，存储指标映射的JSON格式

#### chapter_index格式
```json
{
  "8849-123456-123456": "个人贷款业务总余额",
  "8849-123456-123457": "企业贷款业务总余额"
}
```

### 向量化存储
需要对**标题**和**摘要**进行embedding存储到pgvector，用于向量搜索。

## 核心组件

### 1. DocumentProcessingService
文档处理服务，负责：
- Markdown解析为章节
- LLM生成章节摘要
- 协调向量搜索和模板化处理

### 2. VectorSearchService
向量搜索服务，负责：
- 基于标题和摘要的向量搜索
- 筛选匹配度0.8以上的chunk块
- 返回匹配结果

### 3. TemplatingService
模板化处理服务，负责：
- LLM识别需要填充的位置
- 用模板标记包裹
- 提取和填充模板标记

### 4. TextReportGenerator
主服务类，整合所有功能：
- 新业务流程的统一入口
- 提供完整的API接口

## 模板标记格式

### 标记格式
```
{{指标ID}}
```

### 示例
```
原文：今年的个人贷款业务总余额为15000万元
模板化后：今年的个人贷款业务总余额为{{8849-123456-123456}}
```

## API接口

### 主要接口

#### 1. 处理文档进行模板化
```
POST /text-report-generator/process-document
```
接收markdown内容和指标列表，返回模板化处理后的章节。

#### 2. 搜索相似章节
```
POST /text-report-generator/search-similar-chapters
```
基于标题和摘要进行向量搜索。

#### 3. 模板化内容
```
POST /text-report-generator/create-template
```
对指定内容进行模板化处理。

#### 4. 填充模板标记
```
POST /text-report-generator/fill-template-markers
```
将模板标记替换为实际数值。

## 使用示例

### 基本使用
```python
from .core.models import DocumentProcessingRequest, IndicatorInfo
from .services import TextReportGenerator

# 准备数据
indicators = [
    IndicatorInfo(
        id="8849-123456-123456",
        id_name="个人贷款业务总余额",
        id_value="15000万元"
    )
]

markdown_content = """
## 贷款业务概览
本月个人贷款业务发展良好...
"""

# 创建请求
request = DocumentProcessingRequest(
    markdown_content=markdown_content,
    indicators=indicators
)

# 处理文档
generator = TextReportGenerator(...)
chapters = await generator.process_document_for_templating(request)

# 填充模板标记
for chapter in chapters:
    if "{{" in chapter.chapter_content:
        filled_content = generator.fill_template_markers(
            chapter.chapter_content,
            {"8849-123456-123456": "15000万元"}
        )
```

### 完整工作流程
```python
# 1. 处理文档
chapters = await generator.process_document_for_templating(request)

# 2. 搜索相似章节（可选）
similar_chapters = await generator.search_similar_chapters(
    title="贷款业务概览",
    summary="描述贷款业务情况"
)

# 3. 提取模板标记
markers = generator.extract_template_markers(content)

# 4. 填充模板标记
filled_content = generator.fill_template_markers(content, values)
```

## 测试

### 运行测试
```bash
cd src/app/text_report_generator
python tests/test_templating_business.py
```

### 运行示例
```bash
python examples/templating_usage_example.py
```

## 关键技术点

### 1. 向量搜索匹配
- 对章节标题和摘要分别进行向量搜索
- 只有同时被标题和摘要召回且分数≥0.8的chunk才被认为匹配
- 使用pgvector进行向量存储和搜索

### 2. LLM模板化处理
- 使用LLM识别文本中需要填充数值的位置
- 智能匹配指标与文本内容的相关性
- 生成专业的模板标记格式

### 3. 数据一致性
- 确保chapter_index字段与实际使用的指标一致
- 模板标记与指标ID的准确映射
- 向量化数据与文本数据的同步

## 注意事项

1. **向量搜索阈值**：匹配分数设置为0.8，可根据实际效果调整
2. **LLM Token限制**：处理长文本时需要分段处理，避免超出token限制
3. **指标匹配准确性**：需要优化prompt以提高指标匹配的准确性
4. **模板标记格式**：严格按照`{{指标ID}}`格式，确保解析正确
5. **数据库兼容性**：新字段需要与现有表结构兼容

## 后续优化方向

1. **向量搜索优化**：调整搜索策略和阈值
2. **LLM Prompt优化**：提高指标匹配和模板化处理的准确性
3. **性能优化**：批量处理和并发优化
4. **错误处理**：完善异常处理和降级策略
5. **监控告警**：添加业务指标监控

## 术语对照

| 旧术语 | 新术语 | 说明 |
|--------|--------|------|
| 挖空 | 模板化 | 更专业的表述 |
| 挖空标记 | 模板标记 | 使用{{}}格式 |
| HollowingService | TemplatingService | 服务名称更新 |
| hollow_content | create_template | 方法名称更新 |
| `<!--指标ID-->` | `{{指标ID}}` | 标记格式更新 |
