"""
向量搜索匹配服务

实现基于标题和摘要的向量搜索，筛选匹配度0.8以上的chunk块
"""

import logging
import json
from typing import List, Dict, Optional, Any
from ..core.models import VectorSearchResult
from modules.knowledge.doc.operations.chunk_ops import ChunkOperation


class VectorSearchService:
    """向量搜索匹配服务"""
    
    def __init__(self, chunk_ops: ChunkOperation, knowledge_id: Optional[str] = None):
        self.chunk_ops = chunk_ops
        self.knowledge_id = knowledge_id or "text_report_generator"
        
    async def search_by_title_and_summary(
        self, title: str, summary: str, min_score: float = 0.8, limit: int = 10
    ) -> List[VectorSearchResult]:
        """
        基于标题和摘要进行向量搜索
        
        Args:
            title: 章节标题
            summary: 章节摘要
            min_score: 最小匹配分数
            limit: 返回结果数量限制
            
        Returns:
            匹配的搜索结果列表
        """
        try:
            logging.info(f"开始向量搜索: title='{title}', summary='{summary}'")
            
            # 搜索标题
            title_results = await self._vector_search_single(title, "title", min_score, limit)
            
            # 搜索摘要
            summary_results = await self._vector_search_single(summary, "summary", min_score, limit)
            
            # 找出同时被标题和摘要召回的chunk_id
            title_chunk_ids = {result.chunk_id for result in title_results}
            summary_chunk_ids = {result.chunk_id for result in summary_results}
            
            # 取交集
            common_chunk_ids = title_chunk_ids.intersection(summary_chunk_ids)
            
            if not common_chunk_ids:
                logging.info("没有找到同时匹配标题和摘要的chunk")
                return []
            
            # 返回匹配的结果
            matched_results = []
            for result in title_results + summary_results:
                if result.chunk_id in common_chunk_ids:
                    matched_results.append(result)
            
            # 去重并按分数排序
            unique_results = {}
            for result in matched_results:
                if result.chunk_id not in unique_results or result.score > unique_results[result.chunk_id].score:
                    unique_results[result.chunk_id] = result
            
            final_results = sorted(unique_results.values(), key=lambda x: x.score, reverse=True)
            logging.info(f"向量搜索完成，找到 {len(final_results)} 个匹配结果")
            
            return final_results
            
        except Exception as e:
            logging.error(f"向量搜索失败: {e}")
            return []
    
    async def _vector_search_single(
        self, query_text: str, search_type: str, min_score: float = 0.8, limit: int = 10
    ) -> List[VectorSearchResult]:
        """
        执行单次向量搜索
        
        Args:
            query_text: 查询文本
            search_type: 搜索类型 (title/summary)
            min_score: 最小匹配分数
            limit: 返回结果数量限制
            
        Returns:
            搜索结果列表
        """
        try:
            if not query_text or not query_text.strip():
                return []
            
            # 使用chunk_ops进行向量搜索
            # 这里需要根据实际的chunk_ops接口进行调整
            search_results = await self._perform_vector_search(
                query_text, search_type, min_score, limit
            )
            
            # 转换为VectorSearchResult格式
            vector_results = []
            for result in search_results:
                try:
                    chunk_info = await self._get_chunk_info(result.get("chunk_id"))
                    if chunk_info:
                        vector_result = VectorSearchResult(
                            chunk_id=result.get("chunk_id"),
                            score=result.get("score", 0.0),
                            chunk_info=chunk_info
                        )
                        vector_results.append(vector_result)
                except Exception as e:
                    logging.warning(f"处理搜索结果失败: {e}")
                    continue
            
            return vector_results
            
        except Exception as e:
            logging.error(f"单次向量搜索失败: {e}")
            return []
    
    async def _perform_vector_search(
        self, query_text: str, search_type: str, min_score: float, limit: int
    ) -> List[Dict[str, Any]]:
        """
        执行实际的向量搜索
        
        这里需要根据实际的向量搜索接口进行实现
        """
        try:
            # 方案1: 如果chunk_ops有直接的向量搜索接口
            if hasattr(self.chunk_ops, 'vector_search'):
                results = await self.chunk_ops.vector_search(
                    knowledge_id=self.knowledge_id,
                    query_text=query_text,
                    min_score=min_score,
                    limit=limit
                )
                return results
            
            # 方案2: 使用embedding + 向量数据库搜索
            elif hasattr(self.chunk_ops, 'embedding_client') and hasattr(self.chunk_ops, 'vdb_client'):
                # 生成查询向量
                embedding_result = self.chunk_ops.embedding_client.invoke(
                    texts=[query_text],
                    user="vector_search_system"
                )
                
                if not embedding_result.embeddings or not embedding_result.embeddings[0]:
                    return []
                
                query_vector = embedding_result.embeddings[0]
                
                # 构建搜索参数
                search_params = {
                    "collection_name": f"{self.knowledge_id}_chunks",
                    "data": [query_vector],
                    "anns_field": "embedding",
                    "param": {"metric_type": "COSINE"},
                    "limit": limit,
                    "expr": f"knowledge_id == '{self.knowledge_id}'",
                    "output_fields": ["chunk_id", "knowledge_id"],
                    "min_score": min_score
                }
                
                # 执行向量搜索
                search_results = await self.chunk_ops.vdb_client.asearch(**search_params)
                
                # 转换结果格式
                results = []
                for result in search_results:
                    results.append({
                        "chunk_id": result.get("chunk_id"),
                        "score": result.get("score", 0.0),
                        "knowledge_id": result.get("knowledge_id")
                    })
                
                return results
            
            # 方案3: 降级处理 - 返回空结果
            else:
                logging.warning("没有可用的向量搜索接口，返回空结果")
                return []
                
        except Exception as e:
            logging.error(f"执行向量搜索失败: {e}")
            return []
    
    async def _get_chunk_info(self, chunk_id: str) -> Optional[Dict[str, Any]]:
        """
        获取chunk的详细信息
        
        Args:
            chunk_id: chunk ID
            
        Returns:
            chunk的详细信息字典
        """
        try:
            # 使用chunk_ops获取chunk信息
            chunk_infos = await self.chunk_ops.get_chunk_infos_by_chunk_id(chunk_id)
            
            if not chunk_infos:
                return None
            
            # 构建chunk信息字典
            chunk_info = {}
            for info in chunk_infos:
                info_type = info.get("info_type")
                info_value = info.get("info_value")
                
                if info_type == "chapter_name":
                    chunk_info["chapter_name"] = info_value
                elif info_type == "chapter_summary":
                    chunk_info["chapter_summary"] = info_value
                elif info_type == "chapter_content":
                    chunk_info["chapter_content"] = info_value
                elif info_type == "chapter_index":
                    try:
                        # 解析JSON格式的指标映射
                        chunk_info["chapter_indicators_map"] = json.loads(info_value)
                    except:
                        chunk_info["chapter_indicators_map"] = {}
                elif info_type == "chapter_referenced_index_id":
                    try:
                        chunk_info["chapter_referenced_index_id"] = json.loads(info_value)
                    except:
                        chunk_info["chapter_referenced_index_id"] = []
            
            return chunk_info
            
        except Exception as e:
            logging.error(f"获取chunk信息失败: {chunk_id}, 错误: {e}")
            return None
    
    async def search_similar_chunks(
        self, query_text: str, search_field: str = "all", min_score: float = 0.8, limit: int = 5
    ) -> List[VectorSearchResult]:
        """
        搜索相似的chunks
        
        Args:
            query_text: 查询文本
            search_field: 搜索字段 (title/summary/content/all)
            min_score: 最小匹配分数
            limit: 返回结果数量限制
            
        Returns:
            匹配的搜索结果列表
        """
        try:
            results = await self._vector_search_single(query_text, search_field, min_score, limit)
            return results
            
        except Exception as e:
            logging.error(f"搜索相似chunks失败: {e}")
            return []
