"""
新业务逻辑API路由示例

展示如何在FastAPI中使用新的挖空业务逻辑
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict
import logging

from .models import (
    DocumentProcessingRequest,
    IndicatorInfo,
    ChapterInfo,
    VectorSearchResult,
    HollowingResult
)
from .services import TextReportGenerator

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/text-report-generator", tags=["文本报告生成器-挖空业务"])


# 依赖注入：获取TextReportGenerator实例
async def get_text_report_generator() -> TextReportGenerator:
    """
    获取TextReportGenerator实例
    实际使用中需要从配置中获取真实的客户端
    """
    # 这里需要根据实际配置获取客户端
    # from service.client_factory import get_client
    # rdb_client = await get_client("database.rdbs.mysql")
    # vdb_client = await get_client("database.vdbs.pgvector")
    # embedding_client = await get_client("model.embeddings.sample")
    # llm_client = await get_client("model.llms.opentrek")
    
    # 示例中返回None，实际使用时需要返回真实实例
    return None


@router.post("/process-document", response_model=List[ChapterInfo])
async def process_document_for_hollowing(
    request: DocumentProcessingRequest,
    generator: TextReportGenerator = Depends(get_text_report_generator)
):
    """
    新业务流程：处理文档进行挖空
    
    这是新业务逻辑的核心接口，接收markdown内容和指标列表，
    返回经过挖空处理的章节列表。
    """
    try:
        logger.info(f"开始处理文档，内容长度: {len(request.markdown_content)}, 指标数量: {len(request.indicators)}")
        
        if not generator:
            raise HTTPException(status_code=500, detail="服务未正确初始化")
        
        # 执行文档处理
        chapters = await generator.process_document_for_hollowing(request)
        
        logger.info(f"文档处理完成，生成了 {len(chapters)} 个章节")
        return chapters
        
    except Exception as e:
        logger.error(f"文档处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"文档处理失败: {str(e)}")


@router.post("/search-similar-chapters", response_model=List[VectorSearchResult])
async def search_similar_chapters(
    title: str,
    summary: str,
    min_score: float = 0.8,
    limit: int = 10,
    generator: TextReportGenerator = Depends(get_text_report_generator)
):
    """
    基于标题和摘要搜索相似章节
    
    用于在向量数据库中搜索与给定标题和摘要相似的章节，
    匹配度达到min_score以上的结果将被返回。
    """
    try:
        logger.info(f"搜索相似章节: title='{title}', summary='{summary}'")
        
        if not generator:
            raise HTTPException(status_code=500, detail="服务未正确初始化")
        
        # 执行向量搜索
        results = await generator.search_similar_chapters(title, summary, min_score, limit)
        
        logger.info(f"搜索完成，找到 {len(results)} 个相似章节")
        return results
        
    except Exception as e:
        logger.error(f"搜索相似章节失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.post("/hollow-content", response_model=HollowingResult)
async def hollow_content(
    content: str,
    indicators: List[IndicatorInfo],
    generator: TextReportGenerator = Depends(get_text_report_generator)
):
    """
    对内容进行挖空处理
    
    使用LLM识别内容中需要填充指标数值的位置，
    并用特殊标记(<!--指标ID-->)进行挖空。
    """
    try:
        logger.info(f"开始挖空处理，内容长度: {len(content)}, 指标数量: {len(indicators)}")
        
        if not generator:
            raise HTTPException(status_code=500, detail="服务未正确初始化")
        
        # 执行挖空处理
        result = await generator.hollow_content(content, indicators)
        
        logger.info(f"挖空处理完成，使用了 {len(result.indicators_used)} 个指标")
        return result
        
    except Exception as e:
        logger.error(f"挖空处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"挖空处理失败: {str(e)}")


@router.post("/fill-hollow-markers")
async def fill_hollow_markers(
    content: str,
    indicator_values: Dict[str, str],
    generator: TextReportGenerator = Depends(get_text_report_generator)
):
    """
    填充挖空标记
    
    将内容中的挖空标记(<!--指标ID-->)替换为实际的指标数值。
    """
    try:
        logger.info(f"开始填充挖空标记，指标数量: {len(indicator_values)}")
        
        if not generator:
            raise HTTPException(status_code=500, detail="服务未正确初始化")
        
        # 执行填充
        filled_content = generator.fill_hollow_markers(content, indicator_values)
        
        logger.info("挖空标记填充完成")
        return {
            "original_content": content,
            "filled_content": filled_content,
            "indicator_values": indicator_values
        }
        
    except Exception as e:
        logger.error(f"填充挖空标记失败: {e}")
        raise HTTPException(status_code=500, detail=f"填充失败: {str(e)}")


@router.get("/extract-hollow-markers")
async def extract_hollow_markers(
    content: str,
    generator: TextReportGenerator = Depends(get_text_report_generator)
):
    """
    提取内容中的挖空标记
    
    从内容中提取所有的挖空标记(<!--指标ID-->)。
    """
    try:
        logger.info("开始提取挖空标记")
        
        if not generator:
            raise HTTPException(status_code=500, detail="服务未正确初始化")
        
        # 提取挖空标记
        markers = generator.extract_hollow_markers(content)
        
        logger.info(f"提取完成，找到 {len(markers)} 个挖空标记")
        return {
            "content": content,
            "hollow_markers": markers,
            "marker_count": len(markers)
        }
        
    except Exception as e:
        logger.error(f"提取挖空标记失败: {e}")
        raise HTTPException(status_code=500, detail=f"提取失败: {str(e)}")


@router.post("/complete-workflow")
async def complete_hollowing_workflow(
    request: DocumentProcessingRequest,
    fill_values: Dict[str, str] = None,
    generator: TextReportGenerator = Depends(get_text_report_generator)
):
    """
    完整的挖空业务流程
    
    这是一个综合接口，展示完整的挖空业务流程：
    1. 处理文档进行挖空
    2. 可选：填充挖空标记
    """
    try:
        logger.info("开始完整的挖空业务流程")
        
        if not generator:
            raise HTTPException(status_code=500, detail="服务未正确初始化")
        
        # 1. 处理文档进行挖空
        chapters = await generator.process_document_for_hollowing(request)
        
        # 2. 如果提供了填充值，则填充挖空标记
        if fill_values:
            for chapter in chapters:
                if chapter.chapter_content and "<!--" in chapter.chapter_content:
                    chapter.chapter_content = generator.fill_hollow_markers(
                        chapter.chapter_content, fill_values
                    )
        
        # 3. 统计信息
        total_markers = 0
        chapters_with_markers = 0
        
        for chapter in chapters:
            markers = generator.extract_hollow_markers(chapter.chapter_content)
            if markers:
                chapters_with_markers += 1
                total_markers += len(markers)
        
        result = {
            "chapters": chapters,
            "statistics": {
                "total_chapters": len(chapters),
                "chapters_with_markers": chapters_with_markers,
                "total_markers": total_markers,
                "filled": bool(fill_values)
            }
        }
        
        logger.info(f"完整流程完成，处理了 {len(chapters)} 个章节")
        return result
        
    except Exception as e:
        logger.error(f"完整流程失败: {e}")
        raise HTTPException(status_code=500, detail=f"流程失败: {str(e)}")


# 健康检查接口
@router.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": "text-report-generator-hollowing",
        "version": "1.0.0"
    }


# 业务逻辑说明接口
@router.get("/business-info")
async def get_business_info():
    """获取业务逻辑说明"""
    return {
        "business_name": "挖空业务逻辑",
        "description": "从【填数】转变为【挖空】的新业务逻辑",
        "key_features": [
            "Markdown文档解析和章节拆分",
            "LLM生成章节摘要",
            "基于标题和摘要的向量搜索匹配",
            "LLM识别挖空位置并用特殊标记包裹",
            "指标映射存储和管理",
            "挖空标记的提取和填充"
        ],
        "workflow": [
            "1. 接收markdown内容和指标列表",
            "2. 解析为章节并生成摘要",
            "3. 向量搜索匹配已有chunk(0.8+分数)",
            "4. 如果匹配则推荐，否则进行挖空处理",
            "5. 存储挖空后的内容和指标映射"
        ],
        "data_storage": {
            "chapter_index": "JSON格式存储指标映射 {指标id: 指标名称}",
            "hollow_markers": "<!--指标ID--> 格式的挖空标记",
            "vector_search": "基于标题和摘要的向量匹配"
        }
    }
