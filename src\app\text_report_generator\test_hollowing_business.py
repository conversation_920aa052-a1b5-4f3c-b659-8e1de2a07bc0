"""
新业务逻辑测试用例：挖空业务

测试从【填数】到【挖空】的业务转变
"""

import asyncio
import pytest
from unittest.mock import Mock, AsyncMock
from typing import List

from .models import (
    DocumentProcessingRequest,
    IndicatorInfo,
    ChapterInfo,
    VectorSearchResult,
    HollowingResult
)
from .document_processing_service import DocumentProcessingService
from .vector_search_service import VectorSearchService
from .hollowing_service import HollowingService
from .services import TextReportGenerator


class TestHollowingBusiness:
    """挖空业务测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        # 模拟客户端
        self.mock_llm_client = Mock()
        self.mock_chunk_ops = Mock()
        self.mock_rdb_client = Mock()
        self.mock_vdb_client = Mock()
        self.mock_embedding_client = Mock()
        
        # 测试数据
        self.test_indicators = [
            IndicatorInfo(
                id="8849-123456-001",
                id_name="个人贷款业务总余额",
                id_desc="个人贷款业务的总余额",
                id_value="15000万元"
            ),
            IndicatorInfo(
                id="8849-123456-002",
                id_name="企业贷款业务总余额",
                id_desc="企业贷款业务的总余额",
                id_value="25000万元"
            ),
            IndicatorInfo(
                id="8849-123456-003",
                id_name="贷款增长率",
                id_desc="贷款业务的增长率",
                id_value="10%"
            )
        ]
        
        self.test_markdown = """
## 贷款业务概览

本月贷款业务发展良好，个人贷款业务总余额达到了新高。

## 业务增长情况

相比上月，贷款业务实现了稳步增长，增长率达到预期目标。

## 风险控制

风险控制措施得到有效执行，不良贷款率保持在合理水平。
"""
    
    @pytest.mark.asyncio
    async def test_document_processing_service(self):
        """测试文档处理服务"""
        # 设置LLM客户端模拟响应
        mock_summary_response = Mock()
        mock_summary_response.message.content = "这是一个关于贷款业务的章节摘要"
        self.mock_llm_client.ainvoke = AsyncMock(return_value=mock_summary_response)
        
        # 创建服务实例
        service = DocumentProcessingService(
            self.mock_llm_client, 
            self.mock_chunk_ops, 
            "test_knowledge_id"
        )
        
        # 创建请求
        request = DocumentProcessingRequest(
            markdown_content=self.test_markdown,
            indicators=self.test_indicators
        )
        
        # 执行处理
        chapters = await service.process_document(request)
        
        # 验证结果
        assert len(chapters) > 0
        assert all(isinstance(chapter, ChapterInfo) for chapter in chapters)
        assert all(chapter.chapter_summary for chapter in chapters)
        
        print(f"✅ 文档处理测试通过，生成了 {len(chapters)} 个章节")
    
    @pytest.mark.asyncio
    async def test_hollowing_service(self):
        """测试挖空服务"""
        # 设置LLM客户端模拟响应
        mock_match_response = Mock()
        mock_match_response.message.content = '["8849-123456-001", "8849-123456-003"]'
        
        mock_hollow_response = Mock()
        mock_hollow_response.message.content = "本月贷款业务发展良好，个人贷款业务总余额达到了<!--8849-123456-001-->，增长率为<!--8849-123456-003-->"
        
        self.mock_llm_client.ainvoke = AsyncMock(side_effect=[mock_match_response, mock_hollow_response])
        
        # 创建服务实例
        service = HollowingService(self.mock_llm_client)
        
        # 测试内容
        test_content = "本月贷款业务发展良好，个人贷款业务总余额达到了15000万元，增长率为10%"
        
        # 执行挖空处理
        result = await service.hollow_content(test_content, self.test_indicators)
        
        # 验证结果
        assert isinstance(result, HollowingResult)
        assert result.original_content == test_content
        assert "<!--8849-123456-001-->" in result.hollowed_content
        assert "<!--8849-123456-003-->" in result.hollowed_content
        assert len(result.indicators_used) > 0
        
        print(f"✅ 挖空服务测试通过")
        print(f"原始内容: {result.original_content}")
        print(f"挖空后内容: {result.hollowed_content}")
        print(f"使用的指标: {result.indicators_used}")
    
    @pytest.mark.asyncio
    async def test_vector_search_service(self):
        """测试向量搜索服务"""
        # 模拟向量搜索结果
        mock_search_result = [
            {
                "chunk_id": "test-chunk-001",
                "score": 0.85,
                "knowledge_id": "test_knowledge_id"
            }
        ]
        
        # 模拟chunk信息
        mock_chunk_info = [
            {
                "info_type": "chapter_name",
                "info_value": "贷款业务概览"
            },
            {
                "info_type": "chapter_summary", 
                "info_value": "描述贷款业务情况"
            },
            {
                "info_type": "chapter_index",
                "info_value": '{"8849-123456-001": "个人贷款业务总余额"}'
            }
        ]
        
        # 设置模拟方法
        self.mock_chunk_ops.embedding_client = self.mock_embedding_client
        self.mock_chunk_ops.vdb_client = self.mock_vdb_client
        self.mock_chunk_ops.get_chunk_infos_by_chunk_id = AsyncMock(return_value=mock_chunk_info)
        
        # 模拟embedding响应
        mock_embedding_result = Mock()
        mock_embedding_result.embeddings = [[0.1, 0.2, 0.3]]  # 模拟向量
        self.mock_embedding_client.invoke = Mock(return_value=mock_embedding_result)
        
        # 模拟向量搜索响应
        self.mock_vdb_client.asearch = AsyncMock(return_value=mock_search_result)
        
        # 创建服务实例
        service = VectorSearchService(self.mock_chunk_ops, "test_knowledge_id")
        
        # 执行搜索
        results = await service.search_by_title_and_summary(
            title="贷款业务概览",
            summary="描述贷款业务的基本情况",
            min_score=0.8
        )
        
        # 验证结果
        assert isinstance(results, list)
        if results:  # 如果有结果
            assert all(isinstance(result, VectorSearchResult) for result in results)
            assert all(result.score >= 0.8 for result in results)
        
        print(f"✅ 向量搜索服务测试通过，找到 {len(results)} 个结果")
    
    @pytest.mark.asyncio
    async def test_text_report_generator_new_flow(self):
        """测试TextReportGenerator新业务流程"""
        # 创建主服务实例
        generator = TextReportGenerator(
            rdb_client=self.mock_rdb_client,
            vdb_client=self.mock_vdb_client,
            embedding_client=self.mock_embedding_client,
            llm_client=self.mock_llm_client,
            knowledge_id="test_knowledge_id"
        )
        
        # 模拟LLM响应
        mock_summary_response = Mock()
        mock_summary_response.message.content = "这是章节摘要"
        
        mock_hollow_response = Mock()
        mock_hollow_response.message.content = "挖空后的内容<!--8849-123456-001-->"
        
        self.mock_llm_client.ainvoke = AsyncMock(side_effect=[
            mock_summary_response, mock_summary_response, mock_summary_response,  # 摘要生成
            Mock(message=Mock(content='["8849-123456-001"]')),  # 指标匹配
            mock_hollow_response  # 挖空处理
        ])
        
        # 创建文档处理请求
        request = DocumentProcessingRequest(
            markdown_content=self.test_markdown,
            indicators=self.test_indicators
        )
        
        # 执行新业务流程
        chapters = await generator.process_document_for_hollowing(request)
        
        # 验证结果
        assert len(chapters) > 0
        assert all(isinstance(chapter, ChapterInfo) for chapter in chapters)
        
        print(f"✅ TextReportGenerator新业务流程测试通过")
        print(f"处理了 {len(chapters)} 个章节")
        
        # 测试挖空标记提取和填充
        for chapter in chapters:
            if chapter.chapter_content and "<!--" in chapter.chapter_content:
                # 提取挖空标记
                markers = generator.extract_hollow_markers(chapter.chapter_content)
                print(f"章节 '{chapter.chapter_name}' 包含挖空标记: {markers}")
                
                # 填充挖空标记
                if markers:
                    indicator_values = {marker: "测试数值" for marker in markers}
                    filled_content = generator.fill_hollow_markers(
                        chapter.chapter_content, indicator_values
                    )
                    print(f"填充后内容: {filled_content}")
                    assert "<!--" not in filled_content
    
    def test_hollow_marker_operations(self):
        """测试挖空标记操作"""
        service = HollowingService(self.mock_llm_client)
        
        # 测试提取挖空标记
        content_with_markers = "今年的个人贷款业务总余额为<!--8849-123456-001-->，增长率为<!--8849-123456-003-->"
        markers = service.extract_hollow_markers(content_with_markers)
        
        assert len(markers) == 2
        assert "8849-123456-001" in markers
        assert "8849-123456-003" in markers
        
        # 测试填充挖空标记
        indicator_values = {
            "8849-123456-001": "15000万元",
            "8849-123456-003": "10%"
        }
        filled_content = service.fill_hollow_markers(content_with_markers, indicator_values)
        
        assert "<!--" not in filled_content
        assert "15000万元" in filled_content
        assert "10%" in filled_content
        
        print("✅ 挖空标记操作测试通过")
        print(f"原始内容: {content_with_markers}")
        print(f"填充后内容: {filled_content}")


async def run_tests():
    """运行所有测试"""
    test_instance = TestHollowingBusiness()
    test_instance.setup_method()
    
    print("🚀 开始运行挖空业务测试...")
    
    try:
        await test_instance.test_document_processing_service()
        await test_instance.test_hollowing_service()
        await test_instance.test_vector_search_service()
        await test_instance.test_text_report_generator_new_flow()
        test_instance.test_hollow_marker_operations()
        
        print("🎉 所有测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(run_tests())
