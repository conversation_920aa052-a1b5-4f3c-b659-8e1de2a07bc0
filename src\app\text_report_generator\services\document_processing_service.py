"""
新业务流程：文档处理服务

实现从markdown到章节拆分、摘要生成的完整流程
支持【模板化】业务逻辑
"""

import asyncio
import logging
import json
import re
from typing import List, Dict, Optional, Any
from ..core.models import (
    DocumentProcessingRequest,
    ChapterInfo,
    IndicatorInfo,
    VectorSearchResult,
    TemplatingResult
)
from modules.knowledge.doc.operations.chunk_ops import ChunkOperation
from base.model_serve.model_runtime.entities import PromptMessage, PromptMessageRole
from .vector_search_service import VectorSearchService
from .templating_service import TemplatingService


class DocumentProcessingService:
    """新业务流程的文档处理服务"""
    
    def __init__(self, llm_client, chunk_ops: ChunkOperation, knowledge_id: Optional[str] = None):
        self.llm_client = llm_client
        self.chunk_ops = chunk_ops
        self.knowledge_id = knowledge_id or "text_report_generator"
        self.vector_search_service = VectorSearchService(chunk_ops, knowledge_id)
        self.templating_service = TemplatingService(llm_client)
        
    async def process_document(self, request: DocumentProcessingRequest) -> List[ChapterInfo]:
        """
        处理文档的主要入口点
        
        Args:
            request: 文档处理请求
            
        Returns:
            处理后的章节列表
        """
        try:
            logging.info(f"开始处理文档，指标数量: {len(request.indicators)}")
            
            # 1. 解析markdown为章节
            chapters = self._parse_markdown_to_chapters(request.markdown_content)
            logging.info(f"解析得到 {len(chapters)} 个章节")
            
            # 2. 为每个章节生成摘要
            chapters_with_summary = await self._generate_summaries_for_chapters(chapters)
            
            # 3. 为每个章节进行向量搜索匹配
            processed_chapters = await self._process_chapters_with_vector_search(
                chapters_with_summary, request.indicators
            )
            
            logging.info(f"文档处理完成，返回 {len(processed_chapters)} 个章节")
            return processed_chapters
            
        except Exception as e:
            logging.error(f"文档处理失败: {e}")
            raise
    
    def _parse_markdown_to_chapters(self, content: str) -> List[ChapterInfo]:
        """
        解析Markdown内容为章节，按一级标题(##)进行分割
        """
        lines = content.split("\n")
        chapters = []
        current_title = ""
        current_content = []
        first_part = []
        chapter_index = 1
        
        for line in lines:
            if line.startswith("## "):
                # 保存前一个章节
                if current_title and current_content:
                    chapters.append(
                        ChapterInfo(
                            chapter_name=current_title,
                            chapter_content="\n".join(current_content).strip(),
                            chapter_summary="",  # 稍后生成
                            chapter_referenced_index_id=[],
                            chapter_index=chapter_index,
                            chapter_indicators_map={}
                        )
                    )
                    chapter_index += 1
                elif not current_title and current_content:
                    # 第一个章节前的内容
                    first_part = current_content.copy()
                
                # 开始新章节
                current_title = line[3:].strip()
                current_content = []
            else:
                current_content.append(line)
        
        # 处理最后一个章节
        if current_title and current_content:
            chapters.append(
                ChapterInfo(
                    chapter_name=current_title,
                    chapter_content="\n".join(current_content).strip(),
                    chapter_summary="",
                    chapter_referenced_index_id=[],
                    chapter_index=chapter_index,
                    chapter_indicators_map={}
                )
            )
        elif current_content:  # 没有任何章节标题，全部作为前置内容
            first_part = current_content.copy()
        
        # 处理前置内容
        result = []
        if first_part and any(line.strip() for line in first_part):
            result.append(
                ChapterInfo(
                    chapter_name="文档前言",
                    chapter_content="\n".join(first_part).strip(),
                    chapter_summary="",
                    chapter_referenced_index_id=[],
                    chapter_index=0,
                    chapter_indicators_map={}
                )
            )
        
        result.extend(chapters)
        
        # 如果没有任何章节，创建一个默认章节
        if not result:
            result = [
                ChapterInfo(
                    chapter_name="文档内容",
                    chapter_content=content.strip(),
                    chapter_summary="",
                    chapter_referenced_index_id=[],
                    chapter_index=1,
                    chapter_indicators_map={}
                )
            ]
        
        return result
    
    async def _generate_summaries_for_chapters(self, chapters: List[ChapterInfo]) -> List[ChapterInfo]:
        """
        为章节列表生成摘要
        """
        tasks = []
        for chapter in chapters:
            task = self._generate_chapter_summary(chapter.chapter_content)
            tasks.append(task)
        
        summaries = await asyncio.gather(*tasks)
        
        # 更新章节摘要
        for i, summary in enumerate(summaries):
            chapters[i].chapter_summary = summary
        
        return chapters
    
    async def _generate_chapter_summary(self, content: str) -> str:
        """
        使用LLM生成章节摘要
        """
        try:
            # 处理8000token上限的问题
            if len(content) <= 6000:
                prompt = f"请为以下内容生成简洁的摘要（200字以内）：\n\n{content}"
                messages = [PromptMessage(role="user", content=prompt)]
                result = await self.llm_client.ainvoke(
                    prompt_messages=messages, stream=False
                )
            else:
                content_pre = content[:2000]
                content_mid = content[
                    len(content) // 2 - 1000 : len(content) // 2 + 1000
                ]
                content_post = content[-2000:]
                prompt = f"请为以下内容生成简洁的摘要（200字以内）：\n\n内容前半部分：{content_pre}\n\n内容中间部分：{content_mid}\n\n内容后半部分：{content_post}"
                messages = [PromptMessage(role="user", content=prompt)]
                result = await self.llm_client.ainvoke(
                    prompt_messages=messages, stream=False
                )
            return result.message.content.strip()
        except Exception as e:
            logging.error(f"生成章节摘要失败: {e}")
            # 降级处理：截取前100个字符作为摘要
            content = content.strip()
            if len(content) <= 100:
                return content
            return content[:100] + "..."
    
    async def _process_chapters_with_vector_search(
        self, chapters: List[ChapterInfo], indicators: List[IndicatorInfo]
    ) -> List[ChapterInfo]:
        """
        为章节进行向量搜索匹配处理
        """
        processed_chapters = []
        
        for chapter in chapters:
            try:
                # 对标题和摘要进行向量搜索
                matched_chunks = await self._vector_search_for_chapter(chapter)
                
                if matched_chunks:
                    # 如果找到匹配的chunk，直接推荐
                    recommended_chapter = await self._recommend_from_matched_chunks(
                        chapter, matched_chunks, indicators
                    )
                    processed_chapters.append(recommended_chapter)
                else:
                    # 否则，按照老逻辑进行模板化处理
                    templated_chapter = await self._template_chapter_content(chapter, indicators)
                    processed_chapters.append(templated_chapter)
                    
            except Exception as e:
                logging.error(f"处理章节失败: {chapter.chapter_name}, 错误: {e}")
                # 降级处理：返回原章节
                processed_chapters.append(chapter)
        
        return processed_chapters
    
    async def _vector_search_for_chapter(self, chapter: ChapterInfo) -> List[VectorSearchResult]:
        """
        对章节的标题和摘要进行向量搜索
        """
        try:
            # 使用向量搜索服务进行搜索
            results = await self.vector_search_service.search_by_title_and_summary(
                title=chapter.chapter_name,
                summary=chapter.chapter_summary,
                min_score=0.8,
                limit=10
            )
            
            return results
            
        except Exception as e:
            logging.error(f"向量搜索失败: {e}")
            return []
    
    async def _recommend_from_matched_chunks(
        self, chapter: ChapterInfo, matched_chunks: List[VectorSearchResult], indicators: List[IndicatorInfo]
    ) -> ChapterInfo:
        """
        从匹配的chunks中推荐内容
        """
        # 获取最佳匹配的chunk信息
        best_chunk = matched_chunks[0]
        chunk_info = best_chunk.chunk_info
        
        # 提取chunk中的指标信息
        if "chapter_indicators_map" in chunk_info:
            chapter.chapter_indicators_map = chunk_info["chapter_indicators_map"]
            chapter.chapter_referenced_index_id = list(chunk_info["chapter_indicators_map"].keys())
        
        logging.info(f"为章节 {chapter.chapter_name} 推荐了匹配的chunk: {best_chunk.chunk_id}")
        return chapter
    
    async def _template_chapter_content(
        self, chapter: ChapterInfo, indicators: List[IndicatorInfo]
    ) -> ChapterInfo:
        """
        对章节内容进行模板化处理
        """
        try:
            # 使用模板化服务进行处理
            templating_result = await self.templating_service.create_template(
                chapter.chapter_content, indicators
            )
            
            # 更新章节信息
            chapter.chapter_content = templating_result.templated_content
            chapter.chapter_referenced_index_id = list(templating_result.indicators_used.keys())
            chapter.chapter_indicators_map = templating_result.indicators_used
            
            return chapter
            
        except Exception as e:
            logging.error(f"模板化处理失败: {e}")
            return chapter
