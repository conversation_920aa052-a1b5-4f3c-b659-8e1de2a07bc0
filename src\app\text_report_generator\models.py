"""
文本报告生成器数据模型

定义了指标信息、章节结构、报告结构等核心数据模型
"""

from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime


class IndicatorInfo(BaseModel):
    """指标信息数据模型"""
    id: str = Field(..., description="指标唯一标识符")
    id_name: str = Field(..., description="指标名称，如'农林牧短期'")
    id_desc: Optional[str] = Field(None, description="指标描述，如'描述了本期农林牧短期贷款'")
    id_value: Optional[ str] = Field(None, description="指标数值")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "id_name": "农林牧短期",
                "id_desc": "描述了本期农林牧短期贷款",
                "id_value": "15000"
            }
        }


class ChapterInfo(BaseModel):
    """章节信息数据模型"""
    chapter_name: str = Field(..., description="章节标题")
    chapter_summary: str = Field(..., description="章节内容摘要")
    chapter_content: str = Field(..., description="章节原始内容")
    chapter_referenced_index_id: List[str] = Field(default_factory=list, description="匹配的指标ID列表")
    chapter_index: Optional[int] = Field(None, description="章节索引")
    # 新增字段：存储章节使用到的指标信息，格式为 {"指标id":"指标名称"}
    chapter_indicators_map: Optional[Dict[str, str]] = Field(default_factory=dict, description="章节指标映射，格式为 {指标id: 指标名称}")

    class Config:
        json_schema_extra = {
            "example": {
                "chapter_name": "贷款总览",
                "chapter_summary": "描述了贷款情况",
                "chapter_content": "本月，农林牧相关的长期贷款为10000元，农林牧相关的短期贷款为5000元...",
                "referenced_index_id": ["8849-123456-123456", "8849-123456-123457"],
                "chapter_index": None,
                "chapter_indicators_map": {"8849-123456-123456": "个人贷款业务总余额", "8849-123456-123457": "企业贷款业务总余额"}
            }
        }


class ReportGenerationRequest(BaseModel):
    """报告生成请求数据模型"""
    series_name: str = Field(..., description="套系名称")
    report_name: str = Field(..., description="报表名称")
    indicators: List[IndicatorInfo] = Field(..., description="报表携带的所有指标信息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "series_name": "月度贷款报告",
                "report_name": "2025年3月贷款总览",
                "indicators": [
                    {
                        "id": 1,
                        "id_name": "农林牧短期",
                        "id_desc": "描述了本期农林牧短期贷款",
                        "id_value": 15000
                    }
                ]
            }
        }


class HistoricalReportQuery(BaseModel):
    """历史报告查询请求"""
    report_name: str = Field(..., description="要查询的报告名称")
    knowledge_id: Optional[str] = Field(None, description="限定知识库业务范围")
    offset: int = Field(0, description="偏移量")
    limit: int = Field(10, description="限制数量")

class HistoricalReportQueryResult(BaseModel):
    """历史报告查询结果"""
    doc_id: str = Field(..., description="文档ID")
    doc_series_name: str = Field(..., description="文档套系名称")
    doc_report_name: str = Field(..., description="文档报表名称")
    doc_timestamp: datetime = Field(..., description="文档时间戳")


class ReferenceDocumentUpload(BaseModel):
    """参考文档上传请求"""
    content: str = Field(..., description="上传的文档内容（Markdown格式）")
    file_name: str = Field(..., description="上传的文件名")


class ChapterGenerationRequest(BaseModel):
    """章节内容生成请求"""
    chapter: ChapterInfo = Field(..., description="章节信息")
    indicators: List[IndicatorInfo] = Field(..., description="用于填充的指标信息")


class ReportConfirmationRequest(BaseModel):
    """报告确认请求"""
    series_name: str = Field(..., description="套系名称")
    report_name: str = Field(..., description="报表名称")
    chapters: List[ChapterInfo] = Field(..., description="确认后的各章节内容")
    timestamp: datetime = Field(default_factory=datetime.now, description="确认时间戳")


class DocumentProcessingRequest(BaseModel):
    """新业务流程：文档处理请求"""
    markdown_content: str = Field(..., description="Markdown格式的文档内容")
    indicators: List[IndicatorInfo] = Field(..., description="完整的指标信息列表")

    class Config:
        json_schema_extra = {
            "example": {
                "markdown_content": "## 贷款业务概览\n本月贷款业务发展良好...",
                "indicators": [
                    {
                        "id": "8849-123456-123456",
                        "id_name": "个人贷款业务总余额",
                        "id_desc": "个人贷款业务的总余额",
                        "id_value": "15000万元"
                    }
                ]
            }
        }


class VectorSearchResult(BaseModel):
    """向量搜索结果"""
    chunk_id: str = Field(..., description="匹配的chunk ID")
    score: float = Field(..., description="匹配分数")
    chunk_info: Dict[str, Any] = Field(..., description="chunk的详细信息")

    class Config:
        json_schema_extra = {
            "example": {
                "chunk_id": "chunk-uuid-123",
                "score": 0.85,
                "chunk_info": {
                    "chapter_name": "贷款业务概览",
                    "chapter_summary": "描述贷款业务情况",
                    "chapter_indicators_map": {"8849-123456-123456": "个人贷款业务总余额"}
                }
            }
        }


class TemplatingResult(BaseModel):
    """模板化处理结果"""
    original_content: str = Field(..., description="原始内容")
    templated_content: str = Field(..., description="模板化后的内容")
    indicators_used: Dict[str, str] = Field(..., description="使用的指标映射")

    class Config:
        json_schema_extra = {
            "example": {
                "original_content": "今年的个人贷款业务总余额为15000万元",
                "templated_content": "今年的个人贷款业务总余额为{{8849-123456-123456}}",
                "indicators_used": {"8849-123456-123456": "个人贷款业务总余额"}
            }
        }

